# NocoBase Markdown Block 操作与 API 参数对应关系

本文档详细描述了 NocoBase 中 Markdown Block 的各种操作与其对应的 API 调用参数，为开发者提供完整的参考指南。

## 目录

1. [Markdown Block 架构概述](#1-markdown-block-架构概述)
2. [基础 Markdown 操作](#2-基础-markdown-操作)
3. [模板渲染机制](#3-模板渲染机制)
4. [变量注入系统](#4-变量注入系统)
5. [数据处理机制](#5-数据处理机制)
6. [前端组件配置示例](#6-前端组件配置示例)
7. [参数传递机制](#7-参数传递机制)

---

## 1. Markdown Block 架构概述

### 核心组件结构

```
Markdown Block (Markdown 区块)
├── MarkdownVoid (主要组件)
│   ├── MarkdownEditor (编辑器模式)
│   │   ├── AntdInput.TextArea (文本输入区)
│   │   ├── VariableSelect (变量选择器)
│   │   └── Action Buttons (操作按钮)
│   └── MarkdownRenderer (渲染模式)
│       ├── parseMarkdown (Markdown 解析)
│       ├── getRenderContent (模板渲染)
│       └── dangerouslySetInnerHTML (HTML 输出)
├── MarkdownFormItemInitializer (表单项初始化器)
├── MarkdownBlockInitializer (区块初始化器)
└── markdownBlockSettings (区块设置)
```

### 关键文件位置

- **MarkdownVoid**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx`
- **Markdown**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.tsx`
- **MarkdownBlockInitializer**: `/packages/core/client/src/modules/blocks/other-blocks/markdown/MarkdownBlockInitializer.tsx`
- **markdownBlockSettings**: `/packages/core/client/src/modules/blocks/other-blocks/markdown/markdownBlockSettings.ts`
- **Markdown Utils**: `/packages/core/client/src/schema-component/antd/markdown/util.ts`
- **Template Engine**: `/packages/core/client/src/schema-component/common/utils/uitls.tsx`

---

## 2. 基础 Markdown 操作

### Edit Markdown (编辑 Markdown)

- **操作类型**: `EditMarkdown`
- **触发方式**: 点击设置中的"Edit markdown"选项
- **API 参数**: 无直接 API 调用，通过 Schema 更新实现

**源码位置**: `/packages/core/client/src/modules/blocks/other-blocks/markdown/markdownBlockSettings.ts:24`

**实现逻辑**:
```typescript
{
  name: 'EditMarkdown',
  type: 'item',
  useComponentProps() {
    const field = useField();
    const { t } = useTranslation();
    return {
      title: t('Edit markdown'),
      onClick: () => {
        field.editable = true; // 切换到编辑模式
      },
    };
  },
}
```

### Save Markdown (保存 Markdown)

- **操作类型**: `onSubmit`
- **触发方式**: 点击编辑器中的"Save"按钮
- **API 参数**: 通过 `dn.emit('patch')` 更新 Schema

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx:244`

**实现逻辑**:
```typescript
onSubmit={async (value) => {
  field.editable = false;
  schema['x-component-props'] ?? (schema['x-component-props'] = {});
  schema['x-component-props']['content'] = value;
  field.componentProps.content = value;
  onSave?.(schema);
  
  // 通过 patch 操作更新 Schema
  dn.emit('patch', {
    schema: {
      'x-uid': schema['x-uid'],
      'x-component-props': {
        content: value,
      },
    },
  });
}}
```

### Cancel Edit (取消编辑)

- **操作类型**: `onCancel`
- **触发方式**: 点击编辑器中的"Cancel"按钮
- **API 参数**: 无 API 调用，仅状态重置

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx:240`

**实现逻辑**:
```typescript
onCancel={() => {
  field.editable = false;
  onCancel?.();
}}
```

### Insert Variable (插入变量)

- **操作类型**: `onInsert`
- **触发方式**: 通过变量选择器选择变量
- **API 参数**: 无直接 API 调用，本地文本处理

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx:61`

**实现逻辑**:
```typescript
const onInsert = useCallback(
  function (paths: string[]) {
    const variable: string[] = paths.filter((key) => Boolean(key.trim()));
    const { current } = inputRef;
    const inputEle = current?.resizableTextArea?.textArea;
    if (!inputEle || !variable) {
      return;
    }
    current.focus();
    
    // 生成模板变量语法
    const templateVar = `{{${paths.join('.')}}}`;
    const startPos = inputEle.selectionStart || 0;
    const newVal = value.substring(0, startPos) + templateVar + value.substring(startPos, value.length);
    const newPos = startPos + templateVar.length;
    setValue(newVal);
    setCurPos(newPos);
  },
  [value],
);
```

---

## 3. 模板渲染机制

### Handlebars 模板引擎

- **引擎类型**: `handlebars`
- **配置位置**: `x-decorator-props.engine`
- **支持的语法**: 标准 Handlebars 语法

**源码位置**: `/packages/core/client/src/schema-component/common/utils/uitls.tsx:111`

**实现逻辑**:
```typescript
export async function getRenderContent(templateEngine, content, variables, localVariables, defaultParse, t?) {
  if (content && templateEngine === 'handlebars') {
    // 注册 Handlebars helper
    Handlebars.registerHelper('t', function (key) {
      if (typeof key === 'string') {
        return t(key, { ns: NAMESPACE_UI_SCHEMA });
      }
      return;
    });
    
    try {
      const safeContent = safeCompile(content);
      const renderedContent = Handlebars.compile(safeContent);
      
      // 处理渲染后的内容
      const data = getVariablesData(localVariables);
      const { $nDate } = variables?.ctxRef?.current || {};
      const variableDate = {};
      Object.keys($nDate || {}).map((v) => {
        variableDate[v] = $nDate[v]();
      });
      
      const html = renderedContent({ 
        ...variables?.ctxRef?.current, 
        ...data, 
        $nDate: variableDate 
      });
      
      return await defaultParse(html);
    } catch (error) {
      if (!/VariablesProvider: .* is not found/.test(error.message)) {
        console.log(error);
      }
      return content;
    }
  } else {
    // 非模板引擎模式
    try {
      const html = await replaceVariableValue(content, variables, localVariables);
      return await defaultParse(html);
    } catch (error) {
      return content;
    }
  }
}
```

### Markdown 解析引擎

- **解析器**: `markdown-it`
- **插件支持**: `highlightjs`, `mermaid`
- **配置选项**: HTML, linkify, typographer, breaks

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/md.ts:14`

**实现逻辑**:
```typescript
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true,
});

md.use(markdownItHighlightjs);
md.use(mermaidPlugin);
```

---

## 4. 变量注入系统

### 变量类型支持

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx:223`

```typescript
const scope = useVariableOptions({
  collectionField: { uiSchema: schema },
  form,
  record,
  uiSchema: schema,
  noDisabled: true,
});
```

### 表单变量联动

- **变量语法**: `{{$nForm.fieldPath}}`
- **联动机制**: 通过 Formily reaction 实现
- **实时更新**: 表单值变化时自动更新 Markdown 内容

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx:183`

**实现逻辑**:
```typescript
function extractNFormPaths(text: string): string[] {
  const regex = /\{\{\$nForm\.([\w.]+)\}\}/g;
  const matches = [...text.matchAll(regex)];
  return matches.map((match) => match[1]);
}

function getValuesByPaths(values: any, paths: string[]): Record<string, any> {
  const result: Record<string, any> = {};

  for (const path of paths) {
    const parts = path.split('.');
    let current = values;

    for (const key of parts) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        current = undefined;
        break;
      }
    }

    result[path] = current;
  }

  return result;
}

// 设置表单联动反应
useEffect(() => {
  if (formVars.length > 0) {
    const id = uid();
    setTimeout(() => {
      currentForm.addEffects(id, () => {
        return reaction(
          () => {
            const result = getValuesByPaths(currentForm.values, formVars);
            return JSON.stringify(result);
          },
          () => {
            setTriggerLinkageUpdate(uid()); // 触发重新渲染
          },
          { fireImmediately: true, equals: isEqual },
        );
      });
    });
    
    return () => {
      currentForm.removeEffects(id);
    };
  }
}, []);
```

### 变量作用域

- **作用域类型**: `markdownBlock`
- **作用域 ID**: 区块 UID
- **变量来源**: 全局变量、局部变量、表单变量、记录变量

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx:276`

```typescript
export const MarkdownVoid = (props) => {
  const flags = useFlag();
  const fieldSchema = useFieldSchema();

  return (
    <VariableScope scopeId={fieldSchema?.['x-uid']} type="markdownBlock">
      <FlagProvider {...flags} collectionField={true}>
        <MarkdownVoidInner {...props} />
      </FlagProvider>
    </VariableScope>
  );
};
```

---

## 5. 数据处理机制

### Markdown 解析和缓存

- **解析函数**: `parseMarkdown`
- **缓存机制**: Lodash memoize
- **异步处理**: 动态导入 markdown-it

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/util.ts:13`

**实现逻辑**:
```typescript
export const parseMarkdown = _.memoize(async (text: string) => {
  if (!text) {
    return text;
  }
  const m = await import('./md');
  return m.default.render(text);
});
```

### React Hook 集成

- **Hook**: `useParseMarkdown`
- **状态管理**: HTML 内容和加载状态
- **副作用**: 内容变化时自动重新解析

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/util.ts:21`

**实现逻辑**:
```typescript
export function useParseMarkdown(text: string) {
  const [html, setHtml] = useState<any>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    parseMarkdown(text)
      .then((r) => {
        setHtml(r);
        setLoading(false);
      })
      .catch((error) => console.log(error));
  }, [text]);

  return { html, loading };
}
```

### 文本转换工具

- **功能**: HTML 转纯文本
- **用途**: 可访问性和搜索功能

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/util.ts:38`

**实现逻辑**:
```typescript
export function convertToText(markdownText: string) {
  const content = markdownText;
  let temp = document.createElement('div');
  temp.innerHTML = content;
  const text = temp.innerText;
  temp = null;
  return text?.replace(/[\n\r]/g, '') || '';
}
```

---

## 6. 前端组件配置示例

### 基本 Markdown 区块配置

```typescript
{
  type: 'void',
  'x-settings': 'blockSettings:markdown',
  'x-decorator': 'CardItem',
  'x-decorator-props': {
    name: 'markdown',
    engine: 'handlebars',  // 模板引擎
  },
  'x-component': 'Markdown.Void',
  'x-editable': false,
  'x-component-props': {
    content: 'This is a demo text, **supports Markdown syntax**.',
  },
}
```

### 表单中的 Markdown 字段配置

```typescript
{
  type: 'string',
  title: '描述',
  'x-decorator': 'FormItem',
  'x-component': 'Markdown',
  'x-collection-field': 'collectionName.description',
  'x-component-props': {
    placeholder: '请输入描述内容',
  },
}
```

### 带变量注入的 Markdown 配置

```typescript
{
  type: 'void',
  'x-settings': 'blockSettings:markdown',
  'x-decorator': 'CardItem',
  'x-component': 'Markdown.Void',
  'x-component-props': {
    content: `
# 用户信息

**用户名**: {{$nForm.username}}
**邮箱**: {{$nForm.email}}
**创建时间**: {{$nForm.createdAt}}

用户状态: {{#if $nForm.isActive}}活跃{{else}}非活跃{{/if}}

## 订单详情
{{#each $nForm.orders}}
- {{this.name}}: {{this.price}}
{{/each}}
    `,
  },
}
```

### Markdown 区块初始化器配置

**源码位置**: `/packages/core/client/src/modules/blocks/other-blocks/markdown/MarkdownBlockInitializer.tsx:15`

```typescript
export const MarkdownBlockInitializer = () => {
  const { insert } = useSchemaInitializer();
  const { t } = useTranslation();
  const itemConfig = useSchemaInitializerItem();

  return (
    <SchemaInitializerItem
      {...itemConfig}
      icon={<FormOutlined />}
      onClick={() => {
        insert({
          type: 'void',
          'x-settings': 'blockSettings:markdown',
          'x-decorator': 'CardItem',
          'x-decorator-props': {
            name: 'markdown',
            engine: 'handlebars',
          },
          'x-component': 'Markdown.Void',
          'x-editable': false,
          'x-component-props': {
            content: t('This is a demo text, **supports Markdown syntax**.'),
          },
        });
      }}
    />
  );
};
```

---

## 7. 参数传递机制

### Schema 更新机制

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx:250`

```typescript
// Schema 更新流程
dn.emit('patch', {
  schema: {
    'x-uid': schema['x-uid'],
    'x-component-props': {
      content: value,  // 更新的内容
    },
  },
});
```

### 模板渲染参数流程

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx:214`

```typescript
// 模板渲染流程
useEffect(() => {
  setLoading(true);
  const cvtContentToHTML = async () => {
    setTimeout(async () => {
      // 1. 模板渲染
      const replacedContent = await getRenderContent(
        engine,           // 模板引擎
        content,          // 原始内容
        variables,         // 全局变量
        localVariables,    // 局部变量
        parseMarkdown,     // Markdown 解析器
        t                 // 翻译函数
      );
      
      // 2. 设置 HTML 内容
      setHtml(replacedContent);
    });
    setLoading(false);
  };
  cvtContentToHTML();
}, [content, variables, localVariables, engine, triggerLinkageUpdate]);
```

### 变量作用域参数

**源码位置**: `/packages/core/client/src/schema-component/antd/markdown/Markdown.Void.tsx:223`

```typescript
// 变量作用域配置
const scope = useVariableOptions({
  collectionField: { uiSchema: schema },  // 集合字段
  form,                                    // 表单实例
  record,                                  // 记录数据
  uiSchema: schema,                        // UI Schema
  noDisabled: true,                        // 包含禁用选项
});
```

### API 调用参数映射

| 操作类型 | 前端 Action | API 方法 | 关键参数 |
|---------|-------------|----------|----------|
| 编辑内容 | `field.editable = true` | 无直接 API | 状态切换 |
| 保存内容 | `dn.emit('patch')` | Schema 更新 | `content`, `x-uid` |
| 插入变量 | `onInsert` | 无直接 API | `paths`, `templateVar` |
| 模板渲染 | `getRenderContent` | 无直接 API | `engine`, `variables` |
| Markdown 解析 | `parseMarkdown` | 无直接 API | `text` |

### 渲染参数结构

```typescript
// 模板渲染参数
interface RenderContentParams {
  templateEngine: 'handlebars' | null;  // 模板引擎类型
  content: string;                      // 原始内容
  variables: VariablesContextType;      // 变量上下文
  localVariables: VariableOption[];     // 局部变量
  defaultParse: (text: string) => Promise<string>;  // 默认解析器
  t?: (key: string) => string;          // 翻译函数
}

// 变量上下文结构
interface VariablesContextType {
  ctxRef: {
    current: {
      $user: Record<string, any>;        // 用户变量
      $form: Record<string, any>;        // 表单变量
      $record: Record<string, any>;      // 记录变量
      $nDate: Record<string, Function>;  // 日期函数
    };
  };
}
```

---

## 总结

本文档详细描述了 NocoBase Markdown Block 的完整操作体系，包括：

1. **基础 Markdown 操作**: 编辑、保存、取消、插入变量
2. **模板渲染机制**: Handlebars 引擎、Markdown 解析、变量替换
3. **变量注入系统**: 变量类型、表单联动、作用域管理
4. **数据处理机制**: 缓存解析、React Hook 集成、文本转换
5. **前端配置**: 基本区块、表单字段、变量注入配置
6. **参数传递**: Schema 更新、模板渲染、变量作用域

所有代码示例都基于 NocoBase 最新源码，确保了技术准确性和实用性。开发者可以基于这份文档准确理解和使用 NocoBase 的 Markdown Block 功能，实现丰富的文档展示和动态内容渲染需求。

---

**文档版本**: v1.0  
**更新日期**: 2025-08-09  
**NocoBase 版本**: 基于最新源码分析