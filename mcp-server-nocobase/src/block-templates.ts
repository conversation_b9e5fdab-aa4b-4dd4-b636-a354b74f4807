/**
 * NocoBase Block Templates
 * 定义各种区块类型的标准模板
 *
 * 核心理念：Schema API 是我们的"眼睛"
 * - 操作前：通过 Schema API 查询页面结构，了解现状
 * - 操作中：基于结构分析，精准定位插入位置
 * - 操作后：通过 Schema API 验证操作结果
 */

import { uid } from './utils.js';
import { NocoBaseClient } from './client.js';

export interface BlockTemplate {
  type: string;
  name: string;
  description: string;
  requiresCollection: boolean;
  createSchema: (options: any) => any;
}

// 生成唯一ID的工具函数
const generateUid = () => uid();

/**
 * 页面结构分析器 - Schema API 作为"眼睛"
 */
export interface PageStructureAnalysis {
  pageUid: string;
  hasGrid: boolean;
  gridUid?: string;
  rows: Array<{
    uid: string;
    cols: Array<{
      uid: string;
      width?: number;
      blocks: Array<{
        uid: string;
        type: string;
        component: string;
        decorator?: string;
        collection?: string;
      }>;
    }>;
  }>;
  availableInsertionPoints: Array<{
    type: 'grid' | 'row' | 'col';
    uid: string;
    description: string;
    recommended: boolean;
  }>;
}

/**
 * 通过 Schema API 分析页面结构
 */
export async function analyzePageStructure(
  client: NocoBaseClient,
  pageSchemaUid: string
): Promise<PageStructureAnalysis> {
  const pageProperties = await client.getSchemaProperties(pageSchemaUid);

  const analysis: PageStructureAnalysis = {
    pageUid: pageSchemaUid,
    hasGrid: false,
    rows: [],
    availableInsertionPoints: []
  };

  // 递归分析页面结构
  function analyzeNode(obj: any, path: string = ''): void {
    if (!obj || typeof obj !== 'object') return;

    const uid = obj['x-uid'];
    const component = obj['x-component'];
    const decorator = obj['x-decorator'];

    // 识别 Grid 容器
    if (component === 'Grid') {
      analysis.hasGrid = true;
      analysis.gridUid = uid;

      // 添加 Grid 级别的插入点
      analysis.availableInsertionPoints.push({
        type: 'grid',
        uid: uid,
        description: `Grid container (${uid})`,
        recommended: true
      });
    }

    // 识别 Grid.Row
    if (component === 'Grid.Row') {
      const rowInfo = {
        uid: uid,
        cols: [] as any[]
      };

      // 分析行中的列
      if (obj.properties) {
        for (const [key, colObj] of Object.entries(obj.properties)) {
          if (colObj && typeof colObj === 'object' && (colObj as any)['x-component'] === 'Grid.Col') {
            const colInfo = {
              uid: (colObj as any)['x-uid'],
              width: (colObj as any)['x-component-props']?.width,
              blocks: [] as any[]
            };

            // 分析列中的区块
            if ((colObj as any).properties) {
              for (const [blockKey, blockObj] of Object.entries((colObj as any).properties)) {
                if (blockObj && typeof blockObj === 'object') {
                  const blockInfo = {
                    uid: (blockObj as any)['x-uid'],
                    type: getBlockType(blockObj as any),
                    component: (blockObj as any)['x-component'] || 'unknown',
                    decorator: (blockObj as any)['x-decorator'],
                    collection: (blockObj as any)['x-decorator-props']?.collection
                  };
                  colInfo.blocks.push(blockInfo);
                }
              }
            }

            // 添加列级别的插入点
            analysis.availableInsertionPoints.push({
              type: 'col',
              uid: colInfo.uid,
              description: `Column (width: ${colInfo.width || 'auto'}, ${colInfo.blocks.length} blocks)`,
              recommended: colInfo.blocks.length === 0 // 空列更推荐
            });

            rowInfo.cols.push(colInfo);
          }
        }
      }

      analysis.rows.push(rowInfo);

      // 添加行级别的插入点
      analysis.availableInsertionPoints.push({
        type: 'row',
        uid: uid,
        description: `Row with ${rowInfo.cols.length} columns`,
        recommended: rowInfo.cols.length < 3 // 列数少的行更推荐
      });
    }

    // 递归分析子节点
    if (obj.properties) {
      for (const [key, value] of Object.entries(obj.properties)) {
        analyzeNode(value, `${path}.${key}`);
      }
    }
  }

  analyzeNode(pageProperties);

  // 如果没有 Grid，建议创建一个
  if (!analysis.hasGrid) {
    analysis.availableInsertionPoints.push({
      type: 'grid',
      uid: pageSchemaUid,
      description: 'Page root (no grid found, will create one)',
      recommended: true
    });
  }

  return analysis;
}

/**
 * 识别区块类型的辅助函数
 */
function getBlockType(obj: any): string {
  if (obj['x-decorator']?.includes('TableBlockProvider')) return 'table';
  if (obj['x-decorator']?.includes('FormBlockProvider')) return 'form';
  if (obj['x-decorator']?.includes('DetailsBlockProvider')) return 'details';
  if (obj['x-component'] === 'Markdown.Void') return 'markdown';
  if (obj['x-decorator']?.includes('KanbanBlockProvider')) return 'kanban';
  if (obj['x-decorator']?.includes('ChartBlockProvider')) return 'chart';
  return 'unknown';
}

/**
 * 智能选择最佳插入位置
 */
export function selectBestInsertionPoint(
  analysis: PageStructureAnalysis,
  blockType: string,
  preferences?: {
    preferEmptySpace?: boolean;
    preferNewRow?: boolean;
    maxColumnsPerRow?: number;
  }
): { uid: string; position: string; description: string } {
  const prefs = {
    preferEmptySpace: true,
    preferNewRow: false,
    maxColumnsPerRow: 2,
    ...preferences
  };

  // 1. 优先选择推荐的插入点
  const recommendedPoints = analysis.availableInsertionPoints.filter(p => p.recommended);

  // 2. 如果偏好空白空间，选择空列
  if (prefs.preferEmptySpace) {
    const emptyCols = recommendedPoints.filter(p =>
      p.type === 'col' && p.description.includes('0 blocks')
    );
    if (emptyCols.length > 0 && emptyCols[0]) {
      return {
        uid: emptyCols[0].uid,
        position: 'beforeEnd',
        description: `Insert into empty column: ${emptyCols[0].description}`
      };
    }
  }

  // 3. 如果偏好新行，或者现有行已满
  if (prefs.preferNewRow || analysis.rows.every(row => row.cols.length >= prefs.maxColumnsPerRow)) {
    const gridPoint = analysis.availableInsertionPoints.find(p => p.type === 'grid');
    if (gridPoint) {
      return {
        uid: gridPoint.uid,
        position: 'beforeEnd',
        description: `Create new row in grid: ${gridPoint.description}`
      };
    }
  }

  // 4. 默认选择第一个推荐点
  if (recommendedPoints.length > 0 && recommendedPoints[0]) {
    return {
      uid: recommendedPoints[0].uid,
      position: 'beforeEnd',
      description: `Default insertion: ${recommendedPoints[0].description}`
    };
  }

  // 5. 兜底方案：页面根节点
  return {
    uid: analysis.pageUid,
    position: 'beforeEnd',
    description: 'Fallback: Insert at page root'
  };
}

/**
 * 表格区块模板
 */
export const createTableBlockSchema = (options: {
  collectionName: string;
  dataSource?: string;
  title?: string;
  params?: any;
  association?: string;
}) => {
  const { collectionName, dataSource = 'main', title, params = {}, association } = options;
  const blockUid = generateUid();

  return {
    type: 'void',
    name: blockUid,
    'x-uid': blockUid,
    'x-acl-action': `${association || collectionName}:list`,
    'x-decorator': 'TableBlockProvider',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      action: 'list',
      params: {
        pageSize: 20,
        ...params
      },
      showIndex: true,
      dragSort: false,
      rowKey: 'id'
    },
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:table',
    'x-component': 'CardItem',
    'x-component-props': {
      title: title || `{{t("${collectionName}")}}`,
    },
    properties: {
      actions: {
        type: 'void',
        'x-initializer': 'table:configureActions',
        'x-component': 'ActionBar',
        'x-component-props': {
          style: {
            marginBottom: 'var(--nb-spacing)',
          },
        },
        properties: {},
      },
      [generateUid()]: {
        type: 'array',
        'x-initializer': 'table:configureColumns',
        'x-component': 'TableV2',
        'x-use-component-props': 'useTableBlockProps',
        'x-component-props': {
          rowKey: 'id',
          rowSelection: {
            type: 'checkbox',
          },
        },
        properties: {},
      },
    },
  };
};

/**
 * 表单区块模板
 */
export const createFormBlockSchema = (options: {
  collectionName: string;
  dataSource?: string;
  title?: string;
  type?: 'create' | 'update';
  association?: string;
}) => {
  const { collectionName, dataSource = 'main', title, type = 'create', association } = options;
  const blockUid = generateUid();

  return {
    type: 'void',
    name: blockUid,
    'x-uid': blockUid,
    'x-acl-action': `${association || collectionName}:${type}`,
    'x-decorator': 'FormBlockProvider',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      action: type,
    },
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': `blockSettings:${type}Form`,
    'x-component': 'CardItem',
    'x-component-props': {
      title: title || `{{t("${type === 'create' ? 'Add new' : 'Edit'}")}}`,
    },
    properties: {
      [generateUid()]: {
        type: 'void',
        'x-component': 'FormV2',
        'x-use-component-props': `use${type === 'create' ? 'Create' : 'Update'}FormBlockProps`,
        properties: {
          grid: {
            type: 'void',
            'x-component': 'Grid',
            'x-initializer': 'form:configureFields',
            properties: {},
          },
          actions: {
            type: 'void',
            'x-initializer': `${type}Form:configureActions`,
            'x-component': 'ActionBar',
            'x-component-props': {
              layout: 'one-column',
            },
            properties: {},
          },
        },
      },
    },
  };
};

/**
 * 详情区块模板
 */
export const createDetailsBlockSchema = (options: {
  collectionName: string;
  dataSource?: string;
  title?: string;
  association?: string;
  readPretty?: boolean;
  pageSize?: number;
  includeActions?: boolean;
  includePagination?: boolean;
  customFields?: Array<{
    name: string;
    title?: string;
    component?: string;
    span?: number;
  }>;
}) => {
  const {
    collectionName,
    dataSource = 'main',
    title,
    association,
    readPretty = true,
    pageSize = 1,
    includeActions = true,
    includePagination = false,
    customFields
  } = options;
  const blockUid = generateUid();
  const formUid = generateUid();
  const gridUid = generateUid();

  // 构建字段配置
  let fieldsProperties: any = {};
  if (customFields && customFields.length > 0) {
    customFields.forEach(field => {
      const fieldUid = generateUid();
      fieldsProperties[fieldUid] = {
        type: 'string',
        'x-uid': fieldUid,
        title: field.title || field.name,
        'x-decorator': 'FormItem',
        'x-component': field.component || 'Input.ReadPretty',
        'x-component-props': {
          span: field.span || 12
        },
        'x-read-pretty': readPretty,
        'x-collection-field': field.name,
      };
    });
  }

  // 构建详情区块的属性
  const detailsProperties: any = {
    grid: {
      type: 'void',
      'x-uid': gridUid,
      'x-component': 'Grid',
      'x-initializer': 'details:configureFields',
      properties: fieldsProperties,
    }
  };

  // 添加操作栏
  if (includeActions) {
    const actionsUid = generateUid();
    detailsProperties.actions = {
      type: 'void',
      'x-uid': actionsUid,
      'x-component': 'ActionBar',
      'x-initializer': 'DetailsActionInitializers',
      'x-component-props': {
        style: {
          marginBottom: 24
        }
      },
      properties: {}
    };
  }

  // 添加分页
  if (includePagination) {
    const paginationUid = generateUid();
    detailsProperties.pagination = {
      type: 'void',
      'x-uid': paginationUid,
      'x-component': 'Pagination',
      'x-component-props': {
        useProps: '{{ useDetailsPaginationProps }}'
      }
    };
  }

  return {
    type: 'void',
    name: blockUid,
    'x-uid': blockUid,
    'x-acl-action': `${association || collectionName}:view`,
    'x-decorator': 'DetailsBlockProvider',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      action: association ? 'list' : 'get',
      resource: association || collectionName,
      association: association,
      readPretty,
      params: {
        pageSize
      },
      rowKey: 'id'
    },
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:details',
    'x-component': 'CardItem',
    'x-component-props': {
      title: title || `{{t("Details")}}`,
    },
    properties: {
      [formUid]: {
        type: 'void',
        'x-uid': formUid,
        'x-component': 'Details',
        'x-use-component-props': 'useDetailsBlockProps',
        'x-read-pretty': readPretty,
        properties: detailsProperties,
      },
    },
  };
};

/**
 * Markdown 区块模板
 */
export const createMarkdownBlockSchema = (options: {
  title?: string;
  content?: string;
}) => {
  const { title = 'Markdown', content = '# Hello World\n\nThis is a markdown block.' } = options;
  const blockUid = generateUid();
  const innerUid = generateUid();

  return {
    type: 'void',
    'x-uid': blockUid,
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:markdown',
    'x-component': 'CardItem',
    'x-component-props': {
      title,
    },
    properties: {
      [innerUid]: {
        type: 'void',
        'x-uid': innerUid,
        'x-editable': false,
        'x-component': 'Markdown.Void',
        'x-component-props': {
          content,
        },
      },
    },
  };
};

/**
 * 看板区块模板
 */
export const createKanbanBlockSchema = (options: {
  collectionName: string;
  dataSource?: string;
  groupField: string;
  sortField?: string;
  title?: string;
  association?: string;
}) => {
  const { collectionName, dataSource = 'main', groupField, sortField, title, association } = options;
  
  return {
    type: 'void',
    'x-acl-action': `${association || collectionName}:list`,
    'x-decorator': 'KanbanBlockProvider',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      action: 'list',
      groupField,
      sortField,
      params: {
        paginate: false,
      },
    },
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:kanban',
    'x-component': 'CardItem',
    'x-component-props': {
      title: title || `{{t("${collectionName}")}}`,
    },
    properties: {
      actions: {
        type: 'void',
        'x-initializer': 'kanban:configureActions',
        'x-component': 'ActionBar',
        'x-component-props': {
          style: {
            marginBottom: 'var(--nb-spacing)',
          },
        },
        properties: {},
      },
      [generateUid()]: {
        type: 'array',
        'x-component': 'Kanban',
        'x-use-component-props': 'useKanbanBlockProps',
        properties: {
          card: {
            type: 'void',
            'x-read-pretty': true,
            'x-label-disabled': true,
            'x-decorator': 'BlockItem',
            'x-component': 'Kanban.Card',
            'x-component-props': {
              openMode: 'drawer',
            },
            properties: {
              grid: {
                type: 'void',
                'x-component': 'Grid',
                'x-component-props': { dndContext: false },
                properties: {},
              },
            },
          },
        },
      },
    },
  };
};

/**
 * 创建列表区块Schema
 */
export const createListBlockSchema = (options: {
  collectionName: string;
  dataSource?: string;
  title?: string;
  rowKey?: string;
  templateSchema?: any;
}): any => {
  const { collectionName, dataSource = 'main', title, rowKey = 'id', templateSchema } = options;
  const blockUid = uid();
  const listUid = uid();
  const itemUid = uid();
  const gridUid = uid();
  const actionBarUid = uid();
  const itemActionBarUid = uid();

  return {
    type: 'void',
    'x-uid': blockUid,
    'x-acl-action': `${collectionName}:view`,
    'x-decorator': 'List.Decorator',
    'x-use-decorator-props': 'useListBlockDecoratorProps',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      readPretty: true,
      action: 'list',
      params: {
        pageSize: 10,
      },
      runWhenParamsChanged: true,
      rowKey,
    },
    'x-component': 'CardItem',
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:list',
    'x-component-props': {
      title: title || `${collectionName} List`,
    },
    properties: {
      actionBar: {
        type: 'void',
        'x-uid': actionBarUid,
        'x-initializer': 'list:configureActions',
        'x-component': 'ActionBar',
        'x-component-props': {
          style: {
            marginBottom: 'var(--nb-spacing)',
          },
        },
      },
      list: {
        type: 'array',
        'x-uid': listUid,
        'x-component': 'List',
        'x-use-component-props': 'useListBlockProps',
        properties: {
          item: {
            type: 'object',
            'x-uid': itemUid,
            'x-component': 'List.Item',
            'x-read-pretty': true,
            'x-use-component-props': 'useListItemProps',
            properties: {
              grid: templateSchema || {
                type: 'void',
                'x-uid': gridUid,
                'x-component': 'Grid',
                'x-initializer': 'details:configureFields',
              },
              actionBar: {
                type: 'void',
                'x-uid': itemActionBarUid,
                'x-align': 'left',
                'x-initializer': 'list:configureItemActions',
                'x-component': 'ActionBar',
                'x-use-component-props': 'useListActionBarProps',
                'x-component-props': {
                  layout: 'one-column',
                },
              },
            },
          },
        },
      },
    },
  };
};

/**
 * 创建网格卡片区块Schema
 */
export const createGridCardBlockSchema = (options: {
  collectionName: string;
  dataSource?: string;
  title?: string;
  rowKey?: string;
  templateSchema?: any;
  columnCount?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  };
}): any => {
  const { collectionName, dataSource = 'main', title, rowKey = 'id', templateSchema, columnCount } = options;
  const blockUid = uid();
  const listUid = uid();
  const itemUid = uid();
  const gridUid = uid();
  const actionBarUid = uid();
  const itemActionBarUid = uid();

  return {
    type: 'void',
    'x-uid': blockUid,
    'x-acl-action': `${collectionName}:view`,
    'x-decorator': 'GridCard.Decorator',
    'x-use-decorator-props': 'useGridCardBlockDecoratorProps',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      readPretty: true,
      action: 'list',
      params: {
        pageSize: 12,
      },
      runWhenParamsChanged: true,
      rowKey,
      columnCount: columnCount || {
        xs: 1,
        sm: 2,
        md: 3,
        lg: 4,
        xl: 4,
        xxl: 6,
      },
    },
    'x-component': 'BlockItem',
    'x-use-component-props': 'useGridCardBlockItemProps',
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:gridCard',
    'x-component-props': {
      title: title || `${collectionName} Grid Card`,
    },
    properties: {
      actionBar: {
        type: 'void',
        'x-uid': actionBarUid,
        'x-initializer': 'gridCard:configureActions',
        'x-component': 'ActionBar',
        'x-use-component-props': 'useGridCardActionBarProps',
      },
      list: {
        type: 'array',
        'x-uid': listUid,
        'x-component': 'GridCard',
        'x-use-component-props': 'useGridCardBlockProps',
        properties: {
          item: {
            type: 'object',
            'x-uid': itemUid,
            'x-component': 'GridCard.Item',
            'x-read-pretty': true,
            'x-use-component-props': 'useGridCardItemProps',
            properties: {
              grid: templateSchema || {
                type: 'void',
                'x-uid': gridUid,
                'x-component': 'Grid',
                'x-initializer': 'details:configureFields',
              },
              actionBar: {
                type: 'void',
                'x-uid': itemActionBarUid,
                'x-align': 'left',
                'x-initializer': 'gridCard:configureItemActions',
                'x-component': 'ActionBar',
                'x-use-component-props': 'useGridCardActionBarProps',
                'x-component-props': {
                  layout: 'one-column',
                },
              },
            },
          },
        },
      },
    },
  };
};

/**
 * 创建日历区块Schema
 */
export const createCalendarBlockSchema = (options: {
  collectionName: string;
  dataSource?: string;
  title?: string;
  fieldNames: {
    id?: string;
    title: string;
    start: string;
    end?: string;
    colorFieldName?: string;
  };
  showLunar?: boolean;
  defaultView?: 'month' | 'week' | 'day';
  enableQuickCreateEvent?: boolean;
  weekStart?: number;
}): any => {
  const {
    collectionName,
    dataSource = 'main',
    title,
    fieldNames,
    showLunar = false,
    defaultView = 'month',
    enableQuickCreateEvent = true,
    weekStart = 0
  } = options;
  const blockUid = uid();
  const calendarUid = uid();
  const toolBarUid = uid();

  return {
    type: 'void',
    'x-uid': blockUid,
    'x-acl-action': `${collectionName}:list`,
    'x-decorator': 'CalendarBlockProvider',
    'x-use-decorator-props': 'useCalendarBlockDecoratorProps',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      action: 'list',
      fieldNames: {
        id: 'id',
        ...fieldNames,
      },
      params: {
        paginate: false,
      },
      showLunar,
      defaultView,
      enableQuickCreateEvent,
      weekStart,
    },
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:calendar',
    'x-component': 'CardItem',
    'x-component-props': {
      title: title || `${collectionName} Calendar`,
    },
    properties: {
      [calendarUid]: {
        type: 'void',
        'x-component': 'CalendarV2',
        'x-use-component-props': 'useCalendarBlockProps',
        properties: {
          toolBar: {
            type: 'void',
            'x-uid': toolBarUid,
            'x-component': 'CalendarV2.ActionBar',
            'x-component-props': {
              style: {
                marginBottom: 24,
              },
            },
            'x-initializer': 'calendar:configureActions',
            properties: {
              today: {
                type: 'void',
                title: 'Today',
                'x-component': 'CalendarV2.Today',
                'x-action': 'calendar:today',
                'x-align': 'left',
              },
              nav: {
                type: 'void',
                title: 'Navigate',
                'x-component': 'CalendarV2.Nav',
                'x-action': 'calendar:nav',
                'x-align': 'left',
              },
              title: {
                type: 'void',
                title: 'Title',
                'x-component': 'CalendarV2.Title',
                'x-action': 'calendar:title',
                'x-align': 'left',
              },
              viewSelect: {
                type: 'void',
                title: 'View Select',
                'x-component': 'CalendarV2.ViewSelect',
                'x-action': 'calendar:viewSelect',
                'x-align': 'right',
              },
            },
          },
          event: {
            type: 'void',
            'x-component': 'CalendarV2.Event',
            'x-component-props': {
              openMode: 'drawer',
            },
            properties: {
              drawer: {
                type: 'void',
                'x-component': 'Action.Container',
                'x-component-props': {
                  className: 'nb-action-popup',
                },
                title: '{{ t("View record") }}',
                properties: {
                  tabs: {
                    type: 'void',
                    'x-component': 'Tabs',
                    'x-initializer': 'popup:addTab',
                    properties: {
                      tab1: {
                        type: 'void',
                        title: '{{t("Details")}}',
                        'x-component': 'Tabs.TabPane',
                        properties: {
                          grid: {
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'popup:common:addBlock',
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  };
};

/**
 * 创建图表区块Schema
 */
export const createChartBlockSchema = (options: {
  collectionName: string;
  dataSource?: string;
  title?: string;
  chartType?: string;
  config?: any;
}): any => {
  const { collectionName, dataSource = 'main', title, chartType = 'line', config = {} } = options;
  const blockUid = uid();
  const chartUid = uid();
  const actionsUid = uid();
  const gridUid = uid();

  return {
    type: 'void',
    'x-uid': blockUid,
    'x-component': 'ChartCardItem',
    'x-use-component-props': 'useChartBlockCardProps',
    'x-settings': 'chart:block',
    'x-decorator': 'ChartBlockProvider',
    'x-component-props': {
      title: title || `${collectionName} Chart`,
    },
    properties: {
      actions: {
        type: 'void',
        'x-uid': actionsUid,
        'x-component': 'ActionBar',
        'x-component-props': {
          style: {
            marginBottom: 'var(--nb-designer-offset)',
          },
        },
        'x-initializer': 'chartBlock:configureActions',
      },
      [chartUid]: {
        type: 'void',
        'x-component': 'Grid',
        'x-decorator': 'ChartV2Block',
        'x-initializer': 'charts:addBlock',
        'x-decorator-props': {
          collection: collectionName,
          dataSource,
          chartType,
          config: {
            chartType,
            title: title || `${collectionName} Chart`,
            ...config,
          },
        },
        properties: {
          [uid()]: {
            type: 'void',
            'x-component': 'ChartRenderer',
            'x-decorator': 'ChartRendererProvider',
            'x-decorator-props': {
              collection: collectionName,
              dataSource,
              config: {
                chartType,
                title: title || `${collectionName} Chart`,
                ...config,
              },
            },
          },
        },
      },
    },
  };
};

// 区块模板注册表
export const BLOCK_TEMPLATES: Record<string, BlockTemplate> = {
  table: {
    type: 'table',
    name: 'Table Block',
    description: 'Display data in a table format with pagination, sorting, and filtering',
    requiresCollection: true,
    createSchema: createTableBlockSchema,
  },
  form: {
    type: 'form',
    name: 'Form Block',
    description: 'Create or edit records with a form interface',
    requiresCollection: true,
    createSchema: createFormBlockSchema,
  },
  details: {
    type: 'details',
    name: 'Details Block',
    description: 'Display detailed information of a single record',
    requiresCollection: true,
    createSchema: createDetailsBlockSchema,
  },
  list: {
    type: 'list',
    name: 'List Block',
    description: 'Display data in a list format with customizable item layout',
    requiresCollection: true,
    createSchema: createListBlockSchema,
  },
  gridCard: {
    type: 'gridCard',
    name: 'Grid Card Block',
    description: 'Display data in a grid card format with responsive columns',
    requiresCollection: true,
    createSchema: createGridCardBlockSchema,
  },
  calendar: {
    type: 'calendar',
    name: 'Calendar Block',
    description: 'Display data in a calendar view with date/time fields',
    requiresCollection: true,
    createSchema: createCalendarBlockSchema,
  },
  chart: {
    type: 'chart',
    name: 'Chart Block',
    description: 'Display data in various chart formats for data visualization',
    requiresCollection: true,
    createSchema: createChartBlockSchema,
  },
  markdown: {
    type: 'markdown',
    name: 'Markdown Block',
    description: 'Display markdown content',
    requiresCollection: false,
    createSchema: createMarkdownBlockSchema,
  },
  kanban: {
    type: 'kanban',
    name: 'Kanban Block',
    description: 'Display data in a kanban board format',
    requiresCollection: true,
    createSchema: createKanbanBlockSchema,
  },
};
