/**
 * NocoBase Markdown Operations Tools
 * 提供Markdown区块的内容操作功能
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';

/**
 * 更新Markdown内容工具
 */
export const updateMarkdownContentTool: Tool = {
  name: 'update_markdown_content',
  description: 'Update the content of a markdown block',
  inputSchema: {
    type: 'object',
    properties: {
      blockUid: {
        type: 'string',
        description: 'The UID of the markdown block to update'
      },
      content: {
        type: 'string',
        description: 'The new markdown content'
      },
      templateEngine: {
        type: 'string',
        enum: ['handlebars', null],
        description: 'Template engine to use (handlebars or null)',
        default: 'handlebars'
      }
    },
    required: ['blockUid', 'content']
  }
};

export async function handleUpdateMarkdownContent(client: NocoBaseClient, args: any) {
  try {
    const { blockUid, content, templateEngine = 'handlebars' } = args;

    // 更新Markdown区块的内容
    const updateData = {
      'x-component-props': {
        content
      }
    };

    // 如果指定了模板引擎，也更新装饰器属性
    if (templateEngine) {
      updateData['x-decorator-props'] = {
        engine: templateEngine
      };
    }

    await client.updateBlockSchema(blockUid, updateData);

    return {
      content: [
        {
          type: 'text',
          text: `Markdown content updated successfully:\n${JSON.stringify({
            blockUid,
            contentLength: content.length,
            templateEngine,
            preview: content.substring(0, 100) + (content.length > 100 ? '...' : '')
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating markdown content: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 获取Markdown内容工具
 */
export const getMarkdownContentTool: Tool = {
  name: 'get_markdown_content',
  description: 'Get the content of a markdown block',
  inputSchema: {
    type: 'object',
    properties: {
      blockUid: {
        type: 'string',
        description: 'The UID of the markdown block'
      }
    },
    required: ['blockUid']
  }
};

export async function handleGetMarkdownContent(client: NocoBaseClient, args: any) {
  try {
    const { blockUid } = args;

    const blockSchema = await client.getBlockSchema(blockUid);
    const content = blockSchema?.['x-component-props']?.content || '';
    const templateEngine = blockSchema?.['x-decorator-props']?.engine || null;

    return {
      content: [
        {
          type: 'text',
          text: `Markdown content retrieved successfully:\n${JSON.stringify({
            blockUid,
            content,
            templateEngine,
            contentLength: content.length
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving markdown content: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 设置Markdown模板引擎工具
 */
export const setMarkdownTemplateEngineTool: Tool = {
  name: 'set_markdown_template_engine',
  description: 'Set the template engine for a markdown block',
  inputSchema: {
    type: 'object',
    properties: {
      blockUid: {
        type: 'string',
        description: 'The UID of the markdown block'
      },
      engine: {
        type: 'string',
        enum: ['handlebars', null],
        description: 'Template engine to use (handlebars for variable support, null for plain markdown)',
        default: 'handlebars'
      }
    },
    required: ['blockUid', 'engine']
  }
};

export async function handleSetMarkdownTemplateEngine(client: NocoBaseClient, args: any) {
  try {
    const { blockUid, engine } = args;

    const updateData = {
      'x-decorator-props': {
        engine: engine === 'null' ? null : engine
      }
    };

    await client.updateBlockSchema(blockUid, updateData);

    return {
      content: [
        {
          type: 'text',
          text: `Markdown template engine updated successfully:\n${JSON.stringify({
            blockUid,
            engine: engine === 'null' ? null : engine,
            variableSupport: engine === 'handlebars'
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error setting markdown template engine: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 创建带变量的Markdown内容工具
 */
export const createMarkdownWithVariablesTool: Tool = {
  name: 'create_markdown_with_variables',
  description: 'Create markdown content with variable templates',
  inputSchema: {
    type: 'object',
    properties: {
      blockUid: {
        type: 'string',
        description: 'The UID of the markdown block'
      },
      template: {
        type: 'string',
        description: 'Markdown template with variable placeholders'
      },
      variables: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Variable name' },
            path: { type: 'string', description: 'Variable path (e.g., $nForm.username)' },
            description: { type: 'string', description: 'Variable description' }
          },
          required: ['name', 'path']
        },
        description: 'Variables to be used in the template'
      }
    },
    required: ['blockUid', 'template']
  }
};

export async function handleCreateMarkdownWithVariables(client: NocoBaseClient, args: any) {
  try {
    const { blockUid, template, variables = [] } = args;

    // 构建包含变量的Markdown内容
    let content = template;
    
    // 如果提供了变量信息，在内容前添加变量说明
    if (variables.length > 0) {
      const variableDoc = variables.map(v => `- **${v.name}**: \`{{${v.path}}}\`${v.description ? ` - ${v.description}` : ''}`).join('\n');
      content = `<!-- Variables used in this template:\n${variableDoc}\n-->\n\n${template}`;
    }

    const updateData = {
      'x-component-props': {
        content
      },
      'x-decorator-props': {
        engine: 'handlebars' // 启用模板引擎支持变量
      }
    };

    await client.updateBlockSchema(blockUid, updateData);

    return {
      content: [
        {
          type: 'text',
          text: `Markdown with variables created successfully:\n${JSON.stringify({
            blockUid,
            variablesCount: variables.length,
            templateEngine: 'handlebars',
            variables: variables.map(v => ({ name: v.name, path: v.path })),
            contentPreview: content.substring(0, 200) + (content.length > 200 ? '...' : '')
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating markdown with variables: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 解析Markdown内容工具
 */
export const parseMarkdownContentTool: Tool = {
  name: 'parse_markdown_content',
  description: 'Parse markdown content to HTML (for preview purposes)',
  inputSchema: {
    type: 'object',
    properties: {
      content: {
        type: 'string',
        description: 'Markdown content to parse'
      },
      enableHighlight: {
        type: 'boolean',
        description: 'Enable syntax highlighting for code blocks',
        default: true
      },
      enableMermaid: {
        type: 'boolean',
        description: 'Enable Mermaid diagram support',
        default: true
      }
    },
    required: ['content']
  }
};

export async function handleParseMarkdownContent(client: NocoBaseClient, args: any) {
  try {
    const { content, enableHighlight = true, enableMermaid = true } = args;

    // 注意：这里我们只是模拟解析过程，实际的Markdown解析在前端进行
    // 这个工具主要用于内容验证和预览
    const lines = content.split('\n');
    const stats = {
      totalLines: lines.length,
      headings: lines.filter(line => line.trim().startsWith('#')).length,
      codeBlocks: (content.match(/```/g) || []).length / 2,
      links: (content.match(/\[.*?\]\(.*?\)/g) || []).length,
      images: (content.match(/!\[.*?\]\(.*?\)/g) || []).length,
      variables: (content.match(/\{\{.*?\}\}/g) || []).length
    };

    return {
      content: [
        {
          type: 'text',
          text: `Markdown content parsed successfully:\n${JSON.stringify({
            contentLength: content.length,
            stats,
            features: {
              syntaxHighlight: enableHighlight,
              mermaidDiagrams: enableMermaid,
              variableTemplates: stats.variables > 0
            },
            preview: content.substring(0, 300) + (content.length > 300 ? '...' : '')
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error parsing markdown content: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 注册所有Markdown操作工具
 */
export async function registerMarkdownOperationTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 更新Markdown内容工具
  server.registerTool(
    'update_markdown_content',
    {
      title: 'Update Markdown Content',
      description: 'Update the content of a markdown block',
      inputSchema: {
        blockUid: z.string().describe('The UID of the markdown block to update'),
        content: z.string().describe('The new markdown content'),
        templateEngine: z.enum(['handlebars', 'null']).optional().default('handlebars').describe('Template engine to use (handlebars or null)')
      }
    },
    async (args: any) => {
      return await handleUpdateMarkdownContent(client, args);
    }
  );

  // 获取Markdown内容工具
  server.registerTool(
    'get_markdown_content',
    {
      title: 'Get Markdown Content',
      description: 'Get the content of a markdown block',
      inputSchema: {
        blockUid: z.string().describe('The UID of the markdown block')
      }
    },
    async (args: any) => {
      return await handleGetMarkdownContent(client, args);
    }
  );

  // 设置Markdown模板引擎工具
  server.registerTool(
    'set_markdown_template_engine',
    {
      title: 'Set Markdown Template Engine',
      description: 'Set the template engine for a markdown block',
      inputSchema: {
        blockUid: z.string().describe('The UID of the markdown block'),
        engine: z.enum(['handlebars', 'null']).describe('Template engine to use (handlebars for variable support, null for plain markdown)')
      }
    },
    async (args: any) => {
      return await handleSetMarkdownTemplateEngine(client, args);
    }
  );

  // 创建带变量的Markdown内容工具
  server.registerTool(
    'create_markdown_with_variables',
    {
      title: 'Create Markdown with Variables',
      description: 'Create markdown content with variable templates',
      inputSchema: {
        blockUid: z.string().describe('The UID of the markdown block'),
        template: z.string().describe('Markdown template with variable placeholders'),
        variables: z.array(z.object({
          name: z.string().describe('Variable name'),
          path: z.string().describe('Variable path (e.g., $nForm.username)'),
          description: z.string().optional().describe('Variable description')
        })).optional().describe('Variables to be used in the template')
      }
    },
    async (args: any) => {
      return await handleCreateMarkdownWithVariables(client, args);
    }
  );

  // 解析Markdown内容工具
  server.registerTool(
    'parse_markdown_content',
    {
      title: 'Parse Markdown Content',
      description: 'Parse markdown content to HTML (for preview purposes)',
      inputSchema: {
        content: z.string().describe('Markdown content to parse'),
        enableHighlight: z.boolean().optional().default(true).describe('Enable syntax highlighting for code blocks'),
        enableMermaid: z.boolean().optional().default(true).describe('Enable Mermaid diagram support')
      }
    },
    async (args: any) => {
      return await handleParseMarkdownContent(client, args);
    }
  );
}
