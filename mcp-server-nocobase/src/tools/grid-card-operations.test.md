# Grid Card Operations Tools 测试文档

## 概述

本文档用于测试新添加的 Grid Card Operations 工具功能，验证各种网格卡片操作的正确性。

## 测试环境

- NocoBase 测试环境：https://n.astra.xin/apps/mcp_playground
- 登录账号：neo/neo@123
- API Base URL: https://n.astra.xin/api
- App ID: mcp_playground
- Authorization Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM

## 新增的工具列表

### 1. 数据操作工具

#### get_grid_card_data
- **功能**: 获取网格卡片数据，支持分页、筛选、排序
- **参数**: collectionName, page, pageSize, filter, fields, appends, sort, except
- **默认分页**: 12条/页（适合卡片展示）

#### create_grid_card_item
- **功能**: 创建新的网格卡片项
- **参数**: collectionName, values, whitelist, blacklist, updateAssociationValues, triggerWorkflows

#### update_grid_card_item
- **功能**: 更新网格卡片项
- **参数**: collectionName, itemId, values, whitelist, blacklist, updateAssociationValues, forceUpdate, triggerWorkflows

#### delete_grid_card_item
- **功能**: 删除网格卡片项
- **参数**: collectionName, itemId, filter, triggerWorkflows

#### view_grid_card_item
- **功能**: 查看特定网格卡片项
- **参数**: collectionName, itemId, fields, appends, except

### 2. 导入导出工具

#### export_grid_card_data
- **功能**: 导出网格卡片数据为Excel或CSV
- **参数**: collectionName, filter, fields, format
- **支持格式**: xlsx, csv

#### import_grid_card_data
- **功能**: 从Excel文件导入数据到网格卡片
- **参数**: collectionName, file, explain, dryRun

### 3. 配置工具

#### add_grid_card_action
- **功能**: 添加网格卡片操作按钮
- **参数**: gridCardUid, actionType, customConfig, position
- **支持操作**: filter, addNew, refresh, import, export, customRequest, custom

#### configure_grid_card_item_actions
- **功能**: 配置网格卡片项操作按钮
- **参数**: gridCardUid, actions
- **支持操作**: view, edit, delete, popup, updateRecord, customRequest, link, custom

#### configure_grid_card_fields
- **功能**: 配置网格卡片字段显示
- **参数**: gridCardUid, fields
- **支持配置**: 字段组件、属性、网格布局

### 4. 筛选和自定义请求工具

#### filter_grid_card
- **功能**: 对网格卡片数据应用筛选
- **参数**: collectionName, filter, page, pageSize, sort

#### grid_card_custom_request
- **功能**: 发送网格卡片自定义请求
- **参数**: requestId, data, currentRecord, selectedRecords

## 测试用例

### 测试用例 1: 基础数据操作

```bash
# 1. 获取网格卡片数据
echo '{"method": "tools/call", "params": {"name": "get_grid_card_data", "arguments": {"collectionName": "users", "page": 1, "pageSize": 12}}}' | npx mcp-server-nocobase

# 2. 创建网格卡片项
echo '{"method": "tools/call", "params": {"name": "create_grid_card_item", "arguments": {"collectionName": "users", "values": {"nickname": "Test User", "email": "<EMAIL>"}}}}' | npx mcp-server-nocobase

# 3. 查看网格卡片项
echo '{"method": "tools/call", "params": {"name": "view_grid_card_item", "arguments": {"collectionName": "users", "itemId": 1, "fields": ["id", "nickname", "email"]}}}' | npx mcp-server-nocobase
```

### 测试用例 2: 筛选和排序

```bash
# 筛选网格卡片数据
echo '{"method": "tools/call", "params": {"name": "filter_grid_card", "arguments": {"collectionName": "users", "filter": {"nickname": {"$like": "%admin%"}}, "sort": ["-createdAt"]}}}' | npx mcp-server-nocobase
```

### 测试用例 3: 配置操作按钮

```bash
# 添加网格卡片操作按钮
echo '{"method": "tools/call", "params": {"name": "add_grid_card_action", "arguments": {"gridCardUid": "grid_card_uid", "actionType": "addNew"}}}' | npx mcp-server-nocobase

# 配置网格卡片项操作
echo '{"method": "tools/call", "params": {"name": "configure_grid_card_item_actions", "arguments": {"gridCardUid": "grid_card_uid", "actions": [{"actionType": "view"}, {"actionType": "edit"}, {"actionType": "delete"}]}}}' | npx mcp-server-nocobase
```

### 测试用例 4: 字段配置

```bash
# 配置网格卡片字段
echo '{"method": "tools/call", "params": {"name": "configure_grid_card_fields", "arguments": {"gridCardUid": "grid_card_uid", "fields": [{"fieldName": "nickname", "title": "用户名", "component": "Input", "gridProps": {"xs": 24, "sm": 12, "md": 8}}, {"fieldName": "email", "title": "邮箱", "component": "Input", "gridProps": {"xs": 24, "sm": 12, "md": 8}}]}}}' | npx mcp-server-nocobase
```

## 预期结果

1. **数据操作**: 能够正确获取、创建、更新、删除网格卡片项
2. **筛选功能**: 能够根据条件筛选网格卡片数据
3. **配置功能**: 能够添加和配置各种操作按钮
4. **字段配置**: 能够配置字段显示和布局
5. **导入导出**: 能够导入导出网格卡片数据
6. **自定义请求**: 能够发送自定义请求

## 与参考文档的对应关系

### API 端点映射

| 工具功能 | API 端点 | 参考文档对应 |
|---------|----------|-------------|
| get_grid_card_data | GET /{collection}:list | 2.1 筛选操作 |
| create_grid_card_item | POST /{collection}:create | 2.2 新增操作 |
| update_grid_card_item | PUT /{collection}:update | 3.2 编辑操作 |
| delete_grid_card_item | DELETE /{collection}:destroy | 3.3 删除操作 |
| view_grid_card_item | GET /{collection}:get | 3.1 查看操作 |
| export_grid_card_data | POST /{collection}:exportXlsx | 2.5 导出操作 |
| import_grid_card_data | POST /{collection}:importXlsx | 2.4 导入操作 |
| grid_card_custom_request | POST /customRequests:send/{requestId} | 2.6 自定义请求 |

### 权限控制

所有工具都遵循参考文档中的权限控制要求：
- 创建、更新、删除操作需要相应的ACL权限
- 查看操作需要get权限
- 导入导出需要相应的权限
- 筛选和刷新无特殊权限要求

## 注意事项

1. **分页大小**: Grid Card 默认使用12条/页，与参考文档一致
2. **权限检查**: 所有需要权限的操作都包含ACL检查
3. **错误处理**: 所有工具都包含完善的错误处理机制
4. **参数验证**: 使用Zod进行严格的参数验证
5. **响应格式**: 统一的响应格式，便于调试和使用

## 下一步

1. 运行测试用例验证功能
2. 根据测试结果调整和优化
3. 补充更多高级功能（如批量操作、拖拽排序等）
4. 完善文档和示例
